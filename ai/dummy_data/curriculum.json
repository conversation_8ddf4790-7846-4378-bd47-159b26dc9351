{"curricula": [{"id": "curriculum_physics_11_en", "title": "Physics Class 11 - Complete Curriculum", "subject": "Physics", "grade": "11", "duration": "Academic Year (10 months)", "language": "en", "description": "Comprehensive physics curriculum covering mechanics, thermodynamics, waves, and modern physics concepts for Class 11 students", "learningObjectives": ["Understand fundamental concepts of mechanics and motion", "Apply laws of thermodynamics to real-world scenarios", "Analyze wave phenomena and their applications", "Develop problem-solving skills in physics", "Appreciate the role of physics in technology and daily life"], "topics": [{"title": "Physical World and Measurement", "description": "Introduction to physics, units, dimensions, and measurement techniques", "duration": "3 weeks", "subtopics": ["What is Physics?", "Physics, Technology and Society", "Fundamental and Derived Units", "Measurement of Length, Mass and Time", "Accuracy, Precision and Errors", "Significant Figures", "Dimensions of Physical Quantities"], "resources": ["NCERT Physics Part 1 Chapter 1-2"], "activities": ["Laboratory measurements", "Error analysis experiments"], "assessments": ["Unit test", "Practical assessment"]}, {"title": "Kinematics", "description": "Study of motion without considering the causes", "duration": "4 weeks", "subtopics": ["Motion in a Straight Line", "Position, Path Length and Displacement", "Average Velocity and Average Speed", "Instantaneous Velocity and Speed", "Acceleration", "Kinematic Equations", "Motion in a Plane"], "resources": ["NCERT Physics Part 1 Chapter 3-4"], "activities": ["Motion analysis using graphs", "Projectile motion experiments"], "assessments": ["Problem-solving tests", "Graphical analysis"]}, {"title": "Laws of Motion", "description": "Newton's laws and their applications", "duration": "4 weeks", "subtopics": ["<PERSON>'s Fallacy", "The Law of Inertia", "<PERSON>'s First Law of Motion", "<PERSON>'s Second Law of Motion", "<PERSON>'s Third Law of Motion", "Conservation of Momentum", "Equilibrium of a Particle", "Common Forces in Mechanics"], "resources": ["NCERT Physics Part 1 Chapter 5"], "activities": ["Force analysis experiments", "Momentum conservation demos"], "assessments": ["Conceptual tests", "Application problems"]}, {"title": "Work, Energy and Power", "description": "Energy concepts and conservation principles", "duration": "3 weeks", "subtopics": ["Work", "Kinetic Energy", "Work-Energy Theorem", "Potential Energy", "Conservation of Mechanical Energy", "Power", "Collisions"], "resources": ["NCERT Physics Part 1 Chapter 6"], "activities": ["Energy conservation experiments", "Collision analysis"], "assessments": ["Energy problem solving", "Practical work"]}], "prerequisites": ["Basic mathematics (algebra, trigonometry)", "Understanding of graphs and functions", "Elementary knowledge of physical quantities"], "resources": [{"type": "book", "title": "NCERT Physics Class 11 Part 1", "description": "Primary textbook for theoretical concepts"}, {"type": "book", "title": "NCERT Physics Class 11 Part 2", "description": "Continuation of physics concepts"}, {"type": "document", "title": "Physics Laboratory Manual", "description": "Practical experiments and procedures"}], "assessmentStrategy": {"formative": ["Weekly quizzes", "Laboratory reports", "Class participation", "Homework assignments"], "summative": ["Unit tests", "Mid-term examination", "Final examination", "Practical examination"], "weightage": {"assignments": 20, "quizzes": 15, "projects": 15, "exams": 50}}, "difficulty": "intermediate", "tags": ["physics", "mechanics", "grade-11", "ncert", "cbse"], "status": "published", "metadata": {"aiGenerated": true, "model": "gemini-pro", "version": "1.0"}, "usage": {"views": 0, "downloads": 0, "ratings": []}}, {"id": "curriculum_chemistry_11_en", "title": "Chemistry Class 11 - Complete Curriculum", "subject": "Chemistry", "grade": "11", "duration": "Academic Year (10 months)", "language": "en", "description": "Comprehensive chemistry curriculum covering atomic structure, chemical bonding, states of matter, and organic chemistry basics", "learningObjectives": ["Understand atomic structure and periodic properties", "Explain chemical bonding and molecular structure", "Analyze states of matter and their properties", "Apply thermodynamic principles to chemical reactions", "Introduce organic chemistry fundamentals"], "topics": [{"title": "Some Basic Concepts of Chemistry", "description": "Introduction to chemistry, atoms, molecules, and chemical calculations", "duration": "3 weeks", "subtopics": ["Importance of Chemistry", "Nature of Matter", "Properties of Matter and their Measurement", "Laws of Chemical Combination", "<PERSON>'s Atomic Theory", "Atomic and Molecular Masses", "Mole Concept and Molar Masses"], "resources": ["NCERT Chemistry Part 1 Chapter 1"], "activities": ["Mole calculations", "Chemical formula determination"], "assessments": ["Calculation problems", "Conceptual questions"]}, {"title": "Structure of Atom", "description": "Atomic models, electronic configuration, and quantum numbers", "duration": "4 weeks", "subtopics": ["Discovery of Electron, Proton and Neutron", "Atomic Models", "Developments Leading to the Bohr's Model", "<PERSON><PERSON>'s Model for Hydrogen Atom", "Towards Quantum Mechanical Model", "Quantum Mechanical Model of Atom", "Electronic Configuration of Atoms"], "resources": ["NCERT Chemistry Part 1 Chapter 2"], "activities": ["Atomic model comparisons", "Electronic configuration practice"], "assessments": ["Structure diagrams", "Configuration tests"]}], "prerequisites": ["Basic mathematics", "Elementary knowledge of matter", "Understanding of symbols and formulas"], "resources": [{"type": "book", "title": "NCERT Chemistry Class 11 Part 1", "description": "Primary textbook for theoretical concepts"}, {"type": "book", "title": "NCERT Chemistry Class 11 Part 2", "description": "Advanced chemistry topics"}], "assessmentStrategy": {"formative": ["Daily practice problems", "Laboratory observations", "Chemical equation balancing", "Concept mapping"], "summative": ["Chapter tests", "Practical examinations", "Term examinations", "Annual examination"], "weightage": {"assignments": 25, "quizzes": 15, "projects": 10, "exams": 50}}, "difficulty": "intermediate", "tags": ["chemistry", "atomic-structure", "grade-11", "ncert", "cbse"], "status": "published", "metadata": {"aiGenerated": true, "model": "gemini-pro", "version": "1.0"}, "usage": {"views": 0, "downloads": 0, "ratings": []}}, {"id": "curriculum_math_11_en", "title": "Mathematics Class 11 - Complete Curriculum", "subject": "Mathematics", "grade": "11", "duration": "Academic Year (10 months)", "language": "en", "description": "Comprehensive mathematics curriculum covering algebra, trigonometry, calculus, and coordinate geometry for Class 11 students", "learningObjectives": ["Master algebraic operations and equation solving", "Understand trigonometric functions and identities", "Apply coordinate geometry principles", "Develop logical reasoning and problem-solving skills", "Prepare foundation for advanced mathematics"], "topics": [{"title": "Sets and Functions", "description": "Introduction to set theory and function concepts", "duration": "4 weeks", "subtopics": ["Sets and their Representations", "Types of Sets", "Operations on Sets", "Relations and Functions", "Types of Functions"], "resources": ["NCERT Mathematics Part 1 Chapter 1-2"], "activities": ["Set operations practice", "Function mapping exercises"], "assessments": ["Unit test", "Problem-solving assessment"]}, {"title": "Trigonometric Functions", "description": "Comprehensive study of trigonometric ratios and identities", "duration": "5 weeks", "subtopics": ["Angles and their Measurement", "Trigonometric Functions", "Trigonometric Identities", "Trigonometric Equations", "Inverse Trigonometric Functions"], "resources": ["NCERT Mathematics Part 1 Chapter 3"], "activities": ["Identity verification", "Equation solving practice"], "assessments": ["Identity tests", "Application problems"]}], "prerequisites": ["Class 10 mathematics completion", "Basic algebraic operations", "Coordinate geometry fundamentals"], "resources": [{"type": "book", "title": "NCERT Mathematics Class 11 Part 1", "description": "Primary textbook for algebra and trigonometry"}, {"type": "book", "title": "NCERT Mathematics Class 11 Part 2", "description": "Advanced topics in calculus and geometry"}], "assessmentStrategy": {"formative": ["Daily problem solving", "Weekly assignments", "Peer discussions", "Mathematical reasoning tasks"], "summative": ["Chapter tests", "Mid-term examination", "Final examination", "Project work"], "weightage": {"assignments": 20, "quizzes": 20, "projects": 10, "exams": 50}}, "difficulty": "intermediate", "tags": ["mathematics", "algebra", "trigonometry", "grade-11", "ncert", "cbse"], "status": "published", "metadata": {"aiGenerated": true, "model": "gemini-pro", "version": "1.0"}, "usage": {"views": 0, "downloads": 0, "ratings": []}}, {"id": "curriculum_biology_11_en", "title": "Biology Class 11 - Complete Curriculum", "subject": "Biology", "grade": "11", "duration": "Academic Year (10 months)", "language": "en", "description": "Comprehensive biology curriculum covering diversity of life, structural organization, cell biology, and plant physiology", "learningObjectives": ["Understand diversity and classification of living organisms", "Analyze structural organization in plants and animals", "Comprehend cell structure and function", "Study plant and animal physiology", "Develop scientific inquiry and observation skills"], "topics": [{"title": "Diversity in Living World", "description": "Classification and characteristics of living organisms", "duration": "4 weeks", "subtopics": ["What is Living?", "Biodiversity and Need for Classification", "Three Domains of Life", "Kingdom Classification", "Taxonomical Aids"], "resources": ["NCERT Biology Part 1 Chapter 1-2"], "activities": ["Species identification", "Classification exercises"], "assessments": ["Classification tests", "Practical identification"]}], "prerequisites": ["Class 10 science completion", "Basic understanding of life processes", "Elementary knowledge of scientific method"], "resources": [{"type": "book", "title": "NCERT Biology Class 11", "description": "Primary textbook for biological concepts"}], "assessmentStrategy": {"formative": ["Laboratory observations", "Field work reports", "Diagram drawing", "Concept mapping"], "summative": ["Unit tests", "Practical examinations", "Term examinations", "Project presentations"], "weightage": {"assignments": 20, "quizzes": 15, "projects": 15, "exams": 50}}, "difficulty": "intermediate", "tags": ["biology", "diversity", "cell-biology", "grade-11", "ncert", "cbse"], "status": "published", "metadata": {"aiGenerated": true, "model": "gemini-pro", "version": "1.0"}, "usage": {"views": 0, "downloads": 0, "ratings": []}}, {"id": "curriculum_economics_11_en", "title": "Economics Class 11 - Complete Curriculum", "subject": "Economics", "grade": "11", "duration": "Academic Year (10 months)", "language": "en", "description": "Comprehensive economics curriculum covering microeconomics, statistics, and economic development concepts", "learningObjectives": ["Understand basic economic concepts and principles", "Analyze market mechanisms and price determination", "Apply statistical methods to economic data", "Evaluate economic development strategies", "Develop critical thinking about economic issues"], "topics": [{"title": "Introduction to Economics", "description": "Basic concepts and scope of economics", "duration": "3 weeks", "subtopics": ["Meaning and Scope of Economics", "Central Problems of an Economy", "Production Possibility Curve", "Economic Systems", "Microeconomics vs Macroeconomics"], "resources": ["NCERT Economics Part 1 Chapter 1"], "activities": ["Economic problem analysis", "System comparison"], "assessments": ["Conceptual tests", "Case study analysis"]}], "prerequisites": ["Class 10 social science completion", "Basic mathematical skills", "Understanding of social issues"], "resources": [{"type": "book", "title": "NCERT Economics Class 11", "description": "Primary textbook for economic concepts"}], "assessmentStrategy": {"formative": ["Current affairs discussions", "Economic data analysis", "Case study presentations", "Graph interpretation"], "summative": ["Chapter tests", "Statistical projects", "Term examinations", "Research assignments"], "weightage": {"assignments": 25, "quizzes": 15, "projects": 15, "exams": 45}}, "difficulty": "intermediate", "tags": ["economics", "microeconomics", "statistics", "grade-11", "ncert", "cbse"], "status": "published", "metadata": {"aiGenerated": true, "model": "gemini-pro", "version": "1.0"}, "usage": {"views": 0, "downloads": 0, "ratings": []}}]}