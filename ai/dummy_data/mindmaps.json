{"mindMaps": [{"id": "mindmap_physics_11_motion_en", "title": "Laws of Motion - Concept Map", "description": "Visual representation of Newton's laws of motion and their interconnections", "subject": "Physics", "topic": "Laws of Motion", "grade": "11", "language": "en", "difficulty": "intermediate", "layout": {"type": "hierarchical", "direction": "top-bottom", "spacing": {"horizontal": 120, "vertical": 100}}, "style": {"theme": "default", "fontFamily": "<PERSON><PERSON>", "backgroundColor": "#ffffff", "gridEnabled": false}, "settings": {"isInteractive": true, "showLabels": true, "allowEditing": true, "autoLayout": true, "zoomEnabled": true}, "nodes": [{"id": "root_motion", "label": "Laws of Motion", "description": "<PERSON>'s fundamental principles governing motion", "type": "root", "level": 1, "position": {"x": 0, "y": 0}, "style": {"backgroundColor": "#007bff", "textColor": "#ffffff", "borderColor": "#0056b3", "shape": "circle", "fontSize": 18, "fontWeight": "bold"}, "data": {"content": "Three fundamental laws that describe the relationship between forces and motion", "keywords": ["<PERSON>", "Force", "Motion", "Physics"]}, "metadata": {"importance": 5, "difficulty": "medium", "estimatedTime": 5}}, {"id": "first_law", "label": "First Law (Inertia)", "description": "Law of Inertia - objects resist changes in motion", "type": "main_topic", "level": 2, "position": {"x": -200, "y": 150}, "style": {"backgroundColor": "#28a745", "textColor": "#ffffff", "borderColor": "#1e7e34", "shape": "rectangle", "fontSize": 14, "fontWeight": "bold"}, "data": {"content": "An object at rest stays at rest, and an object in motion stays in motion at constant velocity, unless acted upon by an unbalanced force", "examples": ["Seat belts in cars", "Objects on smooth surfaces", "Satellites in orbit"], "keywords": ["Inertia", "Rest", "Motion", "Unbalanced force"]}, "metadata": {"importance": 4, "difficulty": "easy", "estimatedTime": 10}}, {"id": "second_law", "label": "Second Law (F=ma)", "description": "Relationship between force, mass, and acceleration", "type": "main_topic", "level": 2, "position": {"x": 0, "y": 150}, "style": {"backgroundColor": "#ffc107", "textColor": "#212529", "borderColor": "#d39e00", "shape": "rectangle", "fontSize": 14, "fontWeight": "bold"}, "data": {"content": "The acceleration of an object is directly proportional to the net force and inversely proportional to its mass", "examples": ["Pushing a cart", "Throwing a ball", "Car acceleration"], "keywords": ["Force", "Mass", "Acceleration", "Proportional"]}, "metadata": {"importance": 5, "difficulty": "medium", "estimatedTime": 15}}, {"id": "third_law", "label": "Third Law (Action-Reaction)", "description": "Every action has an equal and opposite reaction", "type": "main_topic", "level": 2, "position": {"x": 200, "y": 150}, "style": {"backgroundColor": "#dc3545", "textColor": "#ffffff", "borderColor": "#c82333", "shape": "rectangle", "fontSize": 14, "fontWeight": "bold"}, "data": {"content": "For every action, there is an equal and opposite reaction", "examples": ["Walking", "Swimming", "Rocket propulsion", "Recoil of a gun"], "keywords": ["Action", "Reaction", "Equal", "Opposite"]}, "metadata": {"importance": 4, "difficulty": "medium", "estimatedTime": 12}}, {"id": "applications", "label": "Real-World Applications", "description": "Practical applications of <PERSON>'s laws", "type": "subtopic", "level": 3, "position": {"x": 0, "y": 300}, "style": {"backgroundColor": "#6f42c1", "textColor": "#ffffff", "borderColor": "#59359a", "shape": "ellipse", "fontSize": 12, "fontWeight": "normal"}, "data": {"content": "<PERSON>'s laws explain motion in various real-world scenarios", "examples": ["Transportation", "Sports", "Space exploration", "Safety systems"], "keywords": ["Applications", "Real-world", "Technology"]}, "metadata": {"importance": 3, "difficulty": "easy", "estimatedTime": 8}}], "edges": [{"id": "edge_root_first", "source": "root_motion", "target": "first_law", "label": "includes", "type": "hierarchy", "style": {"strokeColor": "#007bff", "strokeWidth": 2, "strokeStyle": "solid", "arrowType": "arrow"}, "weight": 1}, {"id": "edge_root_second", "source": "root_motion", "target": "second_law", "label": "includes", "type": "hierarchy", "style": {"strokeColor": "#007bff", "strokeWidth": 2, "strokeStyle": "solid", "arrowType": "arrow"}, "weight": 1}, {"id": "edge_root_third", "source": "root_motion", "target": "third_law", "label": "includes", "type": "hierarchy", "style": {"strokeColor": "#007bff", "strokeWidth": 2, "strokeStyle": "solid", "arrowType": "arrow"}, "weight": 1}, {"id": "edge_laws_applications", "source": "second_law", "target": "applications", "label": "leads to", "type": "association", "style": {"strokeColor": "#6c757d", "strokeWidth": 1, "strokeStyle": "dashed", "arrowType": "arrow"}, "weight": 0.5}], "tags": ["physics", "motion", "newton", "grade-11", "ncert"], "status": "published", "metadata": {"aiGenerated": true, "model": "gemini-pro", "version": "1.0", "totalNodes": 5, "totalEdges": 4, "complexity": "simple"}, "usage": {"views": 0, "downloads": 0, "interactions": 0, "ratings": []}, "sharing": {"isPublic": true, "allowDownload": true, "allowComments": true, "collaborators": []}}, {"id": "mindmap_chemistry_11_periodic_table_en", "title": "Periodic Table - Element Classification", "description": "Visual representation of periodic table organization and element properties", "subject": "Chemistry", "topic": "Periodic Table", "grade": "11", "language": "en", "difficulty": "intermediate", "layout": {"type": "radial", "direction": "top-bottom", "spacing": {"horizontal": 150, "vertical": 120}}, "style": {"theme": "colorful", "fontFamily": "<PERSON><PERSON>", "backgroundColor": "#ffffff", "gridEnabled": false}, "settings": {"isInteractive": true, "showLabels": true, "allowEditing": true, "autoLayout": true, "zoomEnabled": true}, "nodes": [{"id": "root_periodic", "label": "Periodic Table", "description": "Organization of chemical elements by atomic number", "type": "root", "level": 1, "position": {"x": 0, "y": 0}, "style": {"backgroundColor": "#dc3545", "textColor": "#ffffff", "borderColor": "#c82333", "shape": "circle", "fontSize": 18, "fontWeight": "bold"}, "data": {"content": "Systematic arrangement of elements based on atomic structure", "keywords": ["Elements", "Atomic Number", "Properties", "Trends"]}, "metadata": {"importance": 5, "difficulty": "medium", "estimatedTime": 5}}, {"id": "groups_periods", "label": "Groups and Periods", "description": "Vertical columns and horizontal rows in periodic table", "type": "main_topic", "level": 2, "position": {"x": -150, "y": 120}, "style": {"backgroundColor": "#28a745", "textColor": "#ffffff", "borderColor": "#1e7e34", "shape": "rectangle", "fontSize": 14, "fontWeight": "bold"}, "data": {"content": "Groups have similar properties, periods show gradual changes", "examples": ["Group 1 - Alkali metals", "Period 3 - Sodium to Argon"], "keywords": ["Groups", "Periods", "Families", "Trends"]}, "metadata": {"importance": 4, "difficulty": "medium", "estimatedTime": 10}}], "edges": [{"id": "edge_root_groups", "source": "root_periodic", "target": "groups_periods", "label": "organized by", "type": "hierarchy", "style": {"strokeColor": "#dc3545", "strokeWidth": 2, "strokeStyle": "solid", "arrowType": "arrow"}, "weight": 1}], "tags": ["chemistry", "periodic-table", "grade-11", "ncert"], "status": "published", "metadata": {"aiGenerated": true, "model": "gemini-pro", "version": "1.0", "totalNodes": 2, "totalEdges": 1, "complexity": "simple"}, "usage": {"views": 0, "downloads": 0, "interactions": 0, "ratings": []}, "sharing": {"isPublic": true, "allowDownload": true, "allowComments": true, "collaborators": []}}, {"id": "mindmap_math_11_functions_en", "title": "Mathematical Functions - Types and Properties", "description": "Visual map of different types of functions and their characteristics", "subject": "Mathematics", "topic": "Functions", "grade": "11", "language": "en", "difficulty": "intermediate", "layout": {"type": "hierarchical", "direction": "left-right", "spacing": {"horizontal": 140, "vertical": 100}}, "style": {"theme": "academic", "fontFamily": "<PERSON><PERSON>", "backgroundColor": "#ffffff", "gridEnabled": true}, "settings": {"isInteractive": true, "showLabels": true, "allowEditing": true, "autoLayout": true, "zoomEnabled": true}, "nodes": [{"id": "root_functions", "label": "Functions", "description": "Mathematical relationships between input and output", "type": "root", "level": 1, "position": {"x": 0, "y": 0}, "style": {"backgroundColor": "#17a2b8", "textColor": "#ffffff", "borderColor": "#138496", "shape": "circle", "fontSize": 18, "fontWeight": "bold"}, "data": {"content": "A relation where each input has exactly one output", "keywords": ["Domain", "Range", "Mapping", "Relation"]}, "metadata": {"importance": 5, "difficulty": "medium", "estimatedTime": 5}}], "edges": [], "tags": ["mathematics", "functions", "grade-11", "ncert"], "status": "published", "metadata": {"aiGenerated": true, "model": "gemini-pro", "version": "1.0", "totalNodes": 1, "totalEdges": 0, "complexity": "simple"}, "usage": {"views": 0, "downloads": 0, "interactions": 0, "ratings": []}, "sharing": {"isPublic": true, "allowDownload": true, "allowComments": true, "collaborators": []}}]}